package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.dto.HubAccessRedirectContext;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.auth.service.ExternalUserMagicLinkService;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantInviteUrlResponse;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantListResponse;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantResponse;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing hub participant lifecycle and data operations.
 * Handles CRUD operations, participant removal, status management, and response conversion.
 */
@Service
public class HubParticipantManagementService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantManagementService.class);

    // Error Messages
    private static final String PARTICIPANT_REMOVAL_FAILED_MESSAGE = "Failed to remove participant";
    private static final String CANNOT_RESEND_INTERNAL_MESSAGE = "Cannot resend invitation to internal participants";

    // Log Messages
    private static final String REMOVING_PARTICIPANT_LOG = "Removing participant {} from hub {} by user {}";
    private static final String PARTICIPANT_REMOVED_LOG = "PARTICIPANT_REMOVED: participantId={}, hubId={}, accountId={}, removedByUserId={}, participantRole={}, wasExternal={}";
    private static final String RESENDING_INVITATION_LOG = "Resending invitation to participant {} in hub {} by user {}";
    private static final String RETRIEVING_PARTICIPANTS_LOG = "Retrieving participants for hub {} with filters: role={}";
    private static final String RETRIEVING_PARTICIPANT_DETAILS_LOG = "Retrieving participant {} details for hub {}";

    private final HubParticipantRepositoryImpl participantRepository;
    private final HubParticipantConverter participantConverter;
    private final CollabHubPermissionService participantPermissionService;
    private final HubParticipantRoleService roleService;
    private final ExternalUserMagicLinkService magicLinkService;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final EmailService emailService;
    private final AuthProperties authProperties;

    @Value("${app.frontend.url}")
    private String frontendUrl;

    public HubParticipantManagementService(HubParticipantRepositoryImpl participantRepository,
                                         HubParticipantConverter participantConverter,
                                         CollabHubPermissionService participantPermissionService,
                                         HubParticipantRoleService roleService,
                                         ExternalUserMagicLinkService magicLinkService,
                                         CollaborationHubRepositoryImpl hubRepository,
                                         EmailService emailService,
                                         AuthProperties authProperties) {
        this.participantRepository = participantRepository;
        this.participantConverter = participantConverter;
        this.participantPermissionService = participantPermissionService;
        this.roleService = roleService;
        this.magicLinkService = magicLinkService;
        this.hubRepository = hubRepository;
        this.emailService = emailService;
        this.authProperties = authProperties;
    }

    /**
     * Gets a paginated list of hub participants with filtering and role-based visibility.
     *
     * @param hubId the hub ID
     * @param pageRequest pagination parameters
     * @param role optional role filter
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return paginated list of participants
     */
    @Transactional(readOnly = true)
    public HubParticipantListResponse getHubParticipants(Long hubId, PageRequest pageRequest,
                                                       HubParticipantRole role) {
        logger.debug(RETRIEVING_PARTICIPANTS_LOG, hubId, role);

        participantPermissionService.validateCanParticipantAccessHubContent(hubId);

        List<HubParticipant> participants = participantRepository.findActiveParticipantsByHubId(hubId);
        long totalElements = participants.size();
        List<HubParticipantResponse> participantResponses = convertParticipantsToResponses(participants);
        HubParticipantListResponse.ParticipantFilters filters = createFilterInfo(role);

        return new HubParticipantListResponse(participantResponses, pageRequest, totalElements, filters);
    }

    /**
     * Gets details of a specific participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @return participant details
     */
    @Transactional(readOnly = true)
    public HubParticipantResponse getParticipantDetails(Long hubId, Long participantId) {
        logger.debug(RETRIEVING_PARTICIPANT_DETAILS_LOG, participantId, hubId);

        participantPermissionService.validateCanParticipantAccessHubContent(hubId);
        HubParticipant participant = participantRepository.findById(participantId);

        String displayName = getParticipantDisplayName(participant);
        return participantConverter.toResponse(participant, displayName);
    }

    /**
     * Removes a participant from a hub with proper validation.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void removeParticipant(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info(REMOVING_PARTICIPANT_LOG, participantId, hubId, userId);

        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);
        if (participant == null) {
            throw new ForbiddenException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND, "Participant not found");
        }

        roleService.validateLastAdminRemoval(participant, userId, hubId);

        performParticipantRemoval(participantId, hubId, participant);
        logParticipantRemoval(participantId, hubId, accountId, userId, participant);
    }

    /**
     * Resends invitation to an external participant with email notification.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void resendInvitation(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info(RESENDING_INVITATION_LOG, participantId, hubId, userId);

        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);
        validateExternalParticipant(participant);

        CollaborationHub hub = hubRepository.findById(hubId);
        String magicToken = updateParticipantWithNewMagicLink(participant, accountId);

        // Send invitation email asynchronously
        sendInvitationEmailAsync(participant.getEmail(), magicToken, hub.getName(), participantId);
    }

    /**
     * Generates a magic link URL for an external participant without sending an email.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return response containing the magic link URL
     */
    @Transactional(readOnly = true)
    public HubParticipantInviteUrlResponse generateParticipantInviteUrl(Long hubId, Long participantId,
                                                                       Long accountId, Long userId) {
        logger.info("Generating invite URL for participant {} in hub {} by user {}", participantId, hubId, userId);

        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);
        validateExternalParticipant(participant);

        // Generate new magic link with hub context
        HubAccessRedirectContext redirectContext = new HubAccessRedirectContext(hubId, participant.getRole().name());
        String magicToken = magicLinkService.createMagicLinkForEmailWithRedirect(
                participant.getEmail(), accountId, redirectContext);

        // Build magic link URL
        String inviteUrl = buildMagicLinkUrl(magicToken);

        // Calculate expiry time
        LocalDateTime expiresAt = LocalDateTime.now()
                .plus(authProperties.getVerificationToken().getMagicLinkTtl());

        logger.info("Generated invite URL for participant {} in hub {}", participantId, hubId);
        return new HubParticipantInviteUrlResponse(
                inviteUrl, participantId, participant.getEmail(),
                participant.getName(), expiresAt);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Converts participants to response DTOs.
     */
    private List<HubParticipantResponse> convertParticipantsToResponses(List<HubParticipant> participants) {
        return participants.stream()
                .map(this::convertParticipantToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Converts a single participant to response DTO.
     */
    private HubParticipantResponse convertParticipantToResponse(HubParticipant participant) {
        String displayName = getParticipantDisplayName(participant);
        return participantConverter.toResponse(participant, displayName);
    }

    /**
     * Gets participant display name.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName();
        }
        
        // Extract from email if name not available
        String email = participant.getEmail();
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf("@"));
        }
        
        return email;
    }

    /**
     * Creates filter information for the response.
     */
    private HubParticipantListResponse.ParticipantFilters createFilterInfo(HubParticipantRole currentRole) {
        // For now, create a simple filter - in production this would have proper filter options
        return new HubParticipantListResponse.ParticipantFilters(
                List.of("active", "pending", "removed"),
                List.of(HubParticipantRole.values()),
                "name",
                currentRole
        );
    }

    /**
     * Ensures that an app-level ADMIN user has a participant entry in the specified hub.
     * If the user is an app-level ADMIN and not already a participant, automatically creates
     * them as a hub admin participant. This allows ADMINs to create content in any hub
     * within their account without needing to be explicitly added as participants.
     *
     * @param hubId the hub ID
     * @param userContext the current user context
     * @return the participant (existing or newly created), or null if user is not admin and not participant
     */
    @Transactional
    public HubParticipant ensureAdminParticipantExists(@NotNull Long hubId, @NotNull UserContext userContext) {
        logger.debug("Ensuring admin participant exists for user {} in hub {}", userContext.getEmail(), hubId);

        // First, try to find existing participant
        HubParticipant existingParticipant = participantRepository.findByHubIdAndEmail(hubId, userContext.getEmail());
        if (existingParticipant != null && existingParticipant.getRemovedAt() == null) {
            logger.debug("Found existing active participant {} for user {} in hub {}",
                    existingParticipant.getId(), userContext.getEmail(), hubId);
            return existingParticipant;
        }

        // If not found and user is not app-level ADMIN, return null
        if (!isAppLevelAdmin(userContext)) {
            logger.debug("User {} is not app-level ADMIN, cannot auto-create participant in hub {}",
                    userContext.getEmail(), hubId);
            return null;
        }

        // Validate that hub exists and belongs to user's account
        if (!validateHubOwnership(hubId, userContext.getAccountId())) {
            logger.warn("App-level ADMIN {} attempted to access hub {} not in their account",
                    userContext.getEmail(), hubId);
            return null;
        }

        // Create new admin participant
        logger.info("Creating admin participant for app-level ADMIN {} in hub {}",
                userContext.getEmail(), hubId);

        HubParticipant newParticipant = participantConverter.createInternalParticipant(
                hubId, userContext.getUserId(), userContext.getEmail(),
                userContext.getDisplayName(), HubParticipantRole.admin);

        participantRepository.insert(newParticipant);

        logger.info("Successfully created admin participant {} for user {} in hub {}",
                newParticipant.getId(), userContext.getEmail(), hubId);

        return newParticipant;
    }

    /**
     * Validates that a participant is external.
     */
    private void validateExternalParticipant(HubParticipant participant) {
        if (!participant.getIsExternal()) {
            throw new ForbiddenException(ErrorCode.INVALID_OPERATION, CANNOT_RESEND_INTERNAL_MESSAGE);
        }
    }

    /**
     * Checks if the user has app-level ADMIN role.
     */
    private boolean isAppLevelAdmin(UserContext userContext) {
        return userContext.isInternalUser() && userContext.getRole() == Role.ADMIN;
    }

    /**
     * Validates that the hub exists and belongs to the specified account.
     */
    private boolean validateHubOwnership(Long hubId, Long accountId) {
        if (accountId == null) {
            return false;
        }

        CollaborationHub hub = hubRepository.findById(hubId);
        return hub != null && hub.getAccountId().equals(accountId);
    }

    /**
     * Updates participant with new magic link and returns the token.
     */
    private String updateParticipantWithNewMagicLink(HubParticipant participant, Long accountId) {
        try {
            // Generate new magic link with hub context
            HubAccessRedirectContext redirectContext = new HubAccessRedirectContext(
                    participant.getHubId(), participant.getRole().name());
            String magicToken = magicLinkService.createMagicLinkForEmailWithRedirect(
                    participant.getEmail(), accountId, redirectContext);

            logger.info("Generated new magic link for participant {} in hub {}",
                       participant.getId(), participant.getHubId());
            return magicToken;
        } catch (Exception e) {
            logger.error("Failed to generate new magic link for participant {}: {}",
                        participant.getId(), e.getMessage());
            throw new ConflictException(ErrorCode.INVALID_OPERATION, "Failed to generate new invitation link");
        }
    }

    /**
     * Sends invitation email asynchronously.
     */
    @Async
    public void sendInvitationEmailAsync(String email, String token, String hubName, Long participantId) {
        try {
            boolean emailSent = emailService.sendMagicLink(email, token, hubName);
            if (emailSent) {
                logger.info("Successfully sent invitation email to {} for participant {}", email, participantId);
            } else {
                logger.warn("Failed to send invitation email to {} for participant {}", email, participantId);
            }
        } catch (Exception e) {
            logger.error("Failed to send invitation email to {} for participant {}: {}",
                        email, participantId, e.getMessage());
        }
    }

    /**
     * Builds the magic link URL with proper formatting.
     */
    private String buildMagicLinkUrl(String magicToken) {
        return frontendUrl + "/auth/magic-link?token=" + magicToken;
    }

    /**
     * Performs the actual participant removal.
     */
    private void performParticipantRemoval(Long participantId, Long hubId, HubParticipant participant) {
        // For now, use the DAO update method - in production this would be a proper repository method
        participant.setRemovedAt(LocalDateTime.now());
        participantRepository.update(participant);
    }

    /**
     * Logs participant removal for audit purposes.
     */
    private void logParticipantRemoval(Long participantId, Long hubId, Long accountId, Long userId, HubParticipant participant) {
        logger.info(PARTICIPANT_REMOVED_LOG, participantId, hubId, accountId, userId, 
                   participant.getRole(), participant.getIsExternal());
    }
}
